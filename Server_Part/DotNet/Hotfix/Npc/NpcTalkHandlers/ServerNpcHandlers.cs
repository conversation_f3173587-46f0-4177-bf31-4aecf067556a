namespace MaoYouJi
{
  [Invoke((long)NpcNameEnum.Mu_JinJin)]
  public class MuJinJinHandler : AInvokeHandler<ServerNpcProcStruct, ETTask>
  {
    public override async ETTask Handle(ServerNpcProcStruct data)
    {
      ServerNpcTalkListResp resp = data.ServerNpcTalkListResp;
      DaTaoShaActComp daTaoShaActComp = GlobalInfoCache.Instance.daTaoShaActComp;
      if (daTaoShaActComp.State != 0)
        resp.talkList = "can_enter_daTaoSha";
      else
        resp.talkList = "default";
      await ETTask.CompletedTask;
    }
  }

  @ServerProcNpcName(NpcNameEnum.LianGongFang_GuanLiYuan)
  public CommonOutParams lianGongFangProc(GetNpcTalkListIn inParams, UserCacheInfo userCacheInfo) {
    GetNpcTalkListOut outParams = new GetNpcTalkListOut();
    User user = userCacheInfo.user;
    if (!user.nowPoint.contains("练功房")) {
      return new CommonOutParams(false, "您不在练功房中！");
    }
    outParams.talkList = "show_liangongfang_time";
    outParams.talkParamsMap = vipBizProc.getLianGongFangSetting(userCacheInfo);
    return outParams;
  }

  @ServerProcNpcName(NpcNameEnum.MaoPu_KuaiDi)
  public CommonOutParams maoPuKuaiDiProc(GetNpcTalkListIn inParams, UserCacheInfo userCacheInfo) {
    User user = userCacheInfo.user;
    GetNpcTalkListOut outParams = new GetNpcTalkListOut();
    outParams.talkList = "default";
    outParams.talkParamsMap = new HashMap<>();
    outParams.talkParamsMap.put("mailNum", String.valueOf(chatProc.countMail(user.getId())));
    return outParams;
  }

  @ServerProcNpcName(NpcNameEnum.LvGuan_GuanLiYuan)
  public CommonOutParams lvGuanGuanLiYuanProc(GetNpcTalkListIn inParams, UserCacheInfo userCacheInfo) {
    GetNpcTalkListOut outParams = new GetNpcTalkListOut();
    outParams.talkList = "show_lvguan_time";
    outParams.talkParamsMap = vipBizProc.getLvGuanSetting(userCacheInfo);
    return outParams;
  }

  @ServerProcNpcName(NpcNameEnum.XiaoHe_Shang)
  public CommonOutParams xiaoHeShangProc(GetNpcTalkListIn inParams, UserCacheInfo userCacheInfo) {
    GetNpcTalkListOut outParams = new GetNpcTalkListOut();
    outParams.talkList = "show_afk_room_time";
    outParams.talkParamsMap = vipBizProc.getAfkSetting(userCacheInfo);
    return outParams;
  }

  @ServerProcNpcName(NpcNameEnum.Zhang_SanFeng)
  public CommonOutParams zhangSanFengProc(GetNpcTalkListIn inParams, UserCacheInfo userCacheInfo) {
    GetNpcTalkListOut outParams = new GetNpcTalkListOut();
    outParams.talkList = "show_afk_room_time";
    outParams.talkParamsMap = vipBizProc.getAfkSetting(userCacheInfo);
    return outParams;
  }

  @ServerProcNpcName(NpcNameEnum.GongChang_GuanLiYuan)
  public CommonOutParams gongChangGuanLiYuanProc(GetNpcTalkListIn inParams, UserCacheInfo userCacheInfo) {
    GetNpcTalkListOut outParams = new GetNpcTalkListOut();
    outParams.talkList = "show_afk_room_time";
    outParams.talkParamsMap = vipBizProc.getAfkSetting(userCacheInfo);
    return outParams;
  }

  @ServerProcNpcName(NpcNameEnum.Wu_DaEr)
  public CommonOutParams wuDaErProc(GetNpcTalkListIn inParams, UserCacheInfo userCacheInfo) {
    User user = userCacheInfo.user;
    GetNpcTalkListOut outParams = new GetNpcTalkListOut();
    if (user.nowAttack.skillMap.containsKey(SkillIdEnum.HunPo_CaiJiShu)) {
      outParams.talkList = "default";
    } else {
      outParams.talkList = "show_learn_hunpo_caijishu";
    }
    return outParams;
  }

  @ServerProcNpcName(NpcNameEnum.Qiu_Ka)
  public CommonOutParams qiuKaProc(GetNpcTalkListIn inParams, UserCacheInfo userCacheInfo) {
    BagSystem bagSystem = userCacheInfo.bagSystem;
    User user = userCacheInfo.user;
    GetNpcTalkListOut outParams = new GetNpcTalkListOut();
    if (bagSystem.GetThingInBag(ThingNameEnum.YingXing_Yi, null, null) != null) {
      outParams.talkList = "default";
    } else {
      MapNode mapNode = ThreadUtil.getMapNode();
      NearPoint nearPoint = mapNode.nears.get(0);
      if (nearPoint != null) {
        MapNode nearMapNode = mapProc.getMapNode(mapNode.mapName, nearPoint.pointName);
        mapProc.changeUserPoint(user, mapNode, nearMapNode);
        chatProc.sendMessageToMapUsers(new SendChatOut("<u>" + user.name + "</u>被俅卡的守卫厌恶地踢开了", ChatType.Local_Chat),
            mapNode, user.id);
        chatProc.sendMessageToMapUsers(new SendChatOut("<u>" + user.name + "</u>被俅卡的守卫赶了出来", ChatType.Local_Chat),
            nearMapNode);
      }
      return new CommonOutParams();
    }
    return outParams;
  }
}